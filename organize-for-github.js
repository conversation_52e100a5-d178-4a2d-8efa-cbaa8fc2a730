#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🚀 开始为GitHub部署整理项目文件...');

// 检查是否在正确的目录
if (!fs.existsSync('package.json')) {
  console.error('❌ 请在项目根目录运行此脚本');
  process.exit(1);
}

// 定义文件移动规则
const fileOperations = {
  // 需要移动到temp目录的文件和文件夹
  moveToTemp: [
    'full_project_package.zip',
    'interactive_storybook_web_updated.zip',
    'ui_files_package.zip',
    'updated_interactive_storybook_files.zip',
    'temp_extract',
    'temp_interactive_updated',
    'temp_ui_extract',
    'temp_updated_extract'
  ],

  // 需要移动到docs目录的文档文件
  moveToDocs: [
    'API测试指南.md',
    'LIBLIB API.md',
    'LIBLIB_API_SETUP.md',
    'QUICK_START.md',
    'QUICK_START_ILLUSTRATIONS.md',
    'UPGRADE_SUMMARY.md',
    'answer_based_illustration_analysis.md',
    'deploy.md',
    'deployment-guide.md',
    'implementation_summary.md',
    'interaction_design.md',
    'performance_report.md',
    'prompt_and_style_strategy.md',
    'story.md',
    'todo.md',
    'ILLUSTRATION_GENERATION_GUIDE.md',
    '使用说明.md',
    '图片配置完成报告.md',
    '基于用户回答生成插画功能 - 分析报告.md'
  ],

  // 需要移动到scripts目录的脚本文件
  moveToScripts: [
    'deploy.bat',
    'deploy.sh',
    'downloadImages.js',
    'generateIllustrations.js',
    'generateStoryIllustrations.js',
    'regeneratePage1.js',
    'test-setup.js',
    'testImg2Img.js',
    'testMain.js',
    'testSingleIllustration.js',
    'verifyImages.js',
    'update_plan.js',
    'update_plan_new.js'
  ],

  // 需要移动到public/images目录的图片文件（如果不在public中）
  moveToPublicImages: [
    'page1.png',
    'page2.png',
    'page3.png',
    'page5.png',
    'page6.png',
    'page7.png',
    'page9.png',
    'page10.png',
    'page12.png'
  ],

  // 需要移动到src/services目录的服务文件
  moveToServices: [
    'apiKeyManager.js',
    'debugLiblibAPI.js',
    'illustrationGenerator.js',
    'promptTemplates.js'
  ],

  // 需要移动到src/data目录的数据文件
  moveToData: [
    'storyData.ts'
  ],

  // 需要移动到src/components/ui目录的UI组件
  moveToUI: [
    'alert.tsx',
    'spinner.tsx'
  ],

  // 需要移动到src/components目录的组件文件
  moveToComponents: [
    'App.tsx',
    'StoryContainer.tsx',
    'StoryPage.tsx'
  ],

  // 需要删除的重复或无用文件
  deleteFiles: [
    'test-api-simple.html',
    'storybook_presentation.py'
  ]
};

// 创建必要的目录结构
const directories = [
  'docs',
  'scripts',
  'temp',
  'public/images',
  'src/components/ui',
  'src/services',
  'src/data',
  'src/utils',
  'src/lib',
  'src/types'
];

console.log('📁 创建目录结构...');
directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ 创建目录: ${dir}`);
  }
});

// 执行文件移动操作
function moveFiles(files, targetDir, description) {
  console.log(`\n📦 ${description}...`);
  files.forEach(file => {
    const sourcePath = path.join('.', file);
    const targetPath = path.join(targetDir, path.basename(file));

    if (fs.existsSync(sourcePath)) {
      try {
        // 如果是目录，使用递归复制
        if (fs.statSync(sourcePath).isDirectory()) {
          copyDirectoryRecursive(sourcePath, targetPath);
          fs.rmSync(sourcePath, { recursive: true, force: true });
        } else {
          // 如果目标文件已存在，先删除
          if (fs.existsSync(targetPath)) {
            fs.unlinkSync(targetPath);
          }
          fs.renameSync(sourcePath, targetPath);
        }
        console.log(`  ✅ 移动: ${file} -> ${targetDir}/`);
      } catch (error) {
        console.log(`  ❌ 移动失败: ${file} - ${error.message}`);
      }
    } else {
      console.log(`  ⚠️  文件不存在: ${file}`);
    }
  });
}

// 递归复制目录
function copyDirectoryRecursive(source, target) {
  if (!fs.existsSync(target)) {
    fs.mkdirSync(target, { recursive: true });
  }

  const files = fs.readdirSync(source);
  files.forEach(file => {
    const sourcePath = path.join(source, file);
    const targetPath = path.join(target, file);

    if (fs.statSync(sourcePath).isDirectory()) {
      copyDirectoryRecursive(sourcePath, targetPath);
    } else {
      fs.copyFileSync(sourcePath, targetPath);
    }
  });
}

// 删除文件
function deleteFiles(files, description) {
  console.log(`\n🗑️  ${description}...`);
  files.forEach(file => {
    const filePath = path.join('.', file);
    if (fs.existsSync(filePath)) {
      try {
        if (fs.statSync(filePath).isDirectory()) {
          fs.rmSync(filePath, { recursive: true, force: true });
        } else {
          fs.unlinkSync(filePath);
        }
        console.log(`  ✅ 删除: ${file}`);
      } catch (error) {
        console.log(`  ❌ 删除失败: ${file} - ${error.message}`);
      }
    }
  });
}

// 执行所有操作
try {
  moveFiles(fileOperations.moveToTemp, 'temp', '移动临时文件到temp目录');
  moveFiles(fileOperations.moveToDocs, 'docs', '移动文档文件到docs目录');
  moveFiles(fileOperations.moveToScripts, 'scripts', '移动脚本文件到scripts目录');
  moveFiles(fileOperations.moveToPublicImages, 'public/images', '移动图片文件到public/images目录');
  moveFiles(fileOperations.moveToServices, 'src/services', '移动服务文件到src/services目录');
  moveFiles(fileOperations.moveToData, 'src/data', '移动数据文件到src/data目录');
  moveFiles(fileOperations.moveToUI, 'src/components/ui', '移动UI组件到src/components/ui目录');
  moveFiles(fileOperations.moveToComponents, 'src/components', '移动组件文件到src/components目录');
  deleteFiles(fileOperations.deleteFiles, '删除无用文件');

  console.log('\n✨ 项目文件整理完成！');
  console.log('\n📋 整理后的项目结构：');
  console.log('├── docs/           # 📚 项目文档');
  console.log('├── scripts/        # 🔧 脚本文件');
  console.log('├── temp/           # 🗂️ 临时文件');
  console.log('├── public/         # 🌐 公共资源');
  console.log('│   └── images/     # 🖼️ 图片资源');
  console.log('├── src/            # 💻 源代码');
  console.log('│   ├── components/ # 🧩 React组件');
  console.log('│   ├── services/   # 🔌 服务层');
  console.log('│   ├── data/       # 📊 数据文件');
  console.log('│   └── utils/      # 🛠️ 工具函数');
  console.log('└── 配置文件        # ⚙️ 项目配置');

  console.log('\n🚀 现在您的项目已经准备好进行GitHub部署了！');
  console.log('\n📋 下一步操作：');
  console.log('1. 运行 npm run build 测试构建');
  console.log('2. 提交到Git: git add . && git commit -m "Organize project for GitHub deployment"');
  console.log('3. 推送到GitHub: git push origin main');
  console.log('4. 在GitHub仓库设置中启用GitHub Pages');

} catch (error) {
  console.error('❌ 整理过程中出现错误:', error.message);
  process.exit(1);
}
